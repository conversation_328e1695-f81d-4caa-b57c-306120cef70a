# dweb-server
The **dweb-server** library allows Rust apps to incorporate a REST server into their binary, providing REST based access to the [Autonomi](https://autonomi.com) peer-to-peer network. The built in server can be customised for this and or other REST based apps running on the local device.

- Capabilities and roadmap: see the features section of the **dweb-cli** [README.md](https://codeberg.org/happybeing/dweb/src/branch/main/dweb-cli#current-features-and-future-plans).

- Add dweb to your Rust project:

  ```
    cargo add dweb-server
  ```

### Status: proof of concept
This library contains only a minimal REST API to demonstrate the capability in combination with Tauri app which uses it:

## Contributions
Contributions under the AGPL3.0 license are welcome and any contributions or PRs submitted will be assumed to be offered under that license unless clearly and prominently specified otherwise. Any contributions are accepted on the condition they conform to that license and the following conditions:

- that by submitting a contribution you are confirming that you are the sole author, understand all the submitted code in depth, and that no AI or other code generation tool that has ingested copyright material was used in generating content in your contribution.

Thanks for understanding that I don't want to accept material of unknown provenance nor spend time reviewing code that the contributor doesn't understand completely.

## LICENSE

Everything is licensed under AGPL3.0 unless otherwise stated.

See also [../LICENSE](../LICENSE)
