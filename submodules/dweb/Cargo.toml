[workspace]
resolver = "2"
members = [ "dweb-cli" , "dweb-lib", "dweb-server"]

[workspace.package]
version = "0.10.9"
authors = ["happybeing <******************>"]
description = "Decentralised web and storage crates for Autonomi"
license = "AGPL-3.0"
repository = "https://codeberg.org/happybeing/dweb"

[workspace.lints.rust]
arithmetic_overflow = "forbid"
mutable_transmutes = "forbid"
no_mangle_const_items = "forbid"
unknown_crate_types = "forbid"
unsafe_code = "warn"
trivial_casts = "warn"
trivial_numeric_casts = "warn"
unused_extern_crates = "warn"
unused_import_braces = "warn"

[workspace.lints.clippy]
uninlined_format_args = "warn"
unicode_not_nfc = "warn"
unused_async = "warn"
unwrap_used = "warn"
clone_on_ref_ptr = "warn"

[profile.release]
debug = 0
strip = "debuginfo"

[profile.dev]
debug = true

