name: build and release

on:
  workflow_dispatch:
  release:
    types: [ created ]

permissions:
  contents: write

jobs:
  build:
    name: ${{ matrix.platform.os_name }} with rust ${{ matrix.toolchain }}
    runs-on: ${{ matrix.platform.os }}
    strategy:
      fail-fast: false
      matrix:
        platform:
#          - os_name: Linux-aarch64
#            os: ubuntu-24.04
#            target: aarch64-unknown-linux-musl
#            bin: dweb-linux-arm64
          - os_name: Linux-x86_64
            os: ubuntu-24.04
            target: x86_64-unknown-linux-gnu
            bin: dweb-linux-amd64
          - os_name: Windows-x86_64
            os: windows-latest
            target: x86_64-pc-windows-msvc
            bin: dweb-amd64.exe
          - os_name: macOS-x86_64
            os: macOS-latest
            target: x86_64-apple-darwin
            bin: dweb-darwin-amd64
          - os_name: macOS-aarch64
            os: macOS-latest
            target: aarch64-apple-darwin
            bin: dweb-darwin-arm64
        toolchain:
          - stable
    steps:
      - uses: actions/checkout@v3
      - name: Build binary
        uses: houseabsolute/actions-rust-cross@v0
        with:
          command: "build"
          target: ${{ matrix.platform.target }}
          toolchain: ${{ matrix.toolchain }}
          args: "--locked --release"
          strip: true
      - name: Rename binary (linux and macos)
        run: mv target/${{ matrix.platform.target }}/release/dweb target/${{ matrix.platform.target }}/release/${{ matrix.platform.bin }}
        if: matrix.platform.os_name != 'Windows-x86_64'
      - name: Rename binary (windows)
        run: mv target/${{ matrix.platform.target }}/release/dweb.exe target/${{ matrix.platform.target }}/release/${{ matrix.platform.bin }}
        if: matrix.platform.os_name == 'Windows-x86_64'
      - name: Generate SHA-256
        run: shasum -a 256 target/${{ matrix.platform.target }}/release/${{ matrix.platform.bin }} | cut -d ' ' -f 1 > target/${{ matrix.platform.target }}/release/${{ matrix.platform.bin }}.sha256
      - name: Release binary and SHA-256 checksum to GitHub
        uses: softprops/action-gh-release@v1
        with:
          files: |
            target/${{ matrix.platform.target }}/release/${{ matrix.platform.bin }}
            target/${{ matrix.platform.target }}/release/${{ matrix.platform.bin }}.sha256
