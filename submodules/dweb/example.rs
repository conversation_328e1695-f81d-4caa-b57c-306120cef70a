use std::sync::Mutex;

use dweb::client::DwebClientConfig;
use dweb_server::DwebService;
use tauri::{Manager, State};

#[tauri::command]
fn start_server(state: State<'_, ServerState>, port: u16) {
    let mut dweb_service = state.dweb_service.lock().unwrap();
    dweb_service.start(port, None);
}

#[tauri::command]
fn dweb_open(_state: State<'_, ServerState>, address_name_or_link: String) {
    let main_server = "http://127.0.0.1:5537";
    let url = format!("{main_server}/dweb-open/{address_name_or_link}");
    println!("dweb_open() opening {url}");
    let _ = open::that(url);
}

struct ServerState {
    dweb_service: Mutex<DwebService>,
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .setup(|app| {
            // Make builtin names such as 'awesome' available (in addition to opening xor addresses)
            dweb::web::name::register_builtin_names(false);
            // Set up dweb service
            app.manage(ServerState {
                dweb_service: Mutex::new(DwebService::new(DwebClientConfig::default())),
            });
            Ok(())
        })
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![start_server, dweb_open])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
