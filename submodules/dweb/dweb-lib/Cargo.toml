[package]
name = "dweb"
description = "Decentralised web and storage library for Autonomi"
edition = "2021"
version.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true

[lib]
name = "dweb"
crate-type = ["cdylib", "rlib"]

[lints]
workspace = true

[features]
default = ["skip-network-compatibility-check"]
metrics = ["ant-logging/process-metrics"]
skip-network-compatibility-check = []   # Avoid need to upload avp type and include xor address in build
development = ["fixed-dweb-hosts"]
fixed-dweb-hosts = []
#vendored = ["dep:utoipa-swagger-ui-vendored"]

[utoipa-swagger-ui.metadata.docs.rs]
features = ["actix-web", "reqwest", "vendored"]
no-default-features = true
rustdoc-args = ["--cfg", "doc_cfg"]

[build-dependencies]
#utoipa-swagger-ui-vendored = { version = "0.1.2", path = "utoipa-swagger-ui-vendored", optional = true }
utoipa-swagger-ui-vendored = "0.1.2"

[dependencies]
#
# Generated using: dw-dependencies --branch stable-2025.7.1.5
autonomi = { version = "0.5.3" }
ant-build-info = { version = "0.1.29" }
ant-logging = { version = "0.2.51" }
evmlib = { version = "0.4.2" }
#
# Generated using: dw-dependencies --branch stable-2025.7.1.3
#autonomi = { version = "0.5.2" }
#ant-build-info = { version = "0.1.29" }
#ant-logging = { version = "0.2.51" }
#evmlib = { version = "0.4.2" }

## Everything else
http = "1.1.0"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
rand = { version = "~0.8.5", features = ["small_rng"] }
anyhow = "1.0.81"
async-stream = "0.3.5"
bytes = "1.6.0"
futures = "0.3.30"
indicatif = { version = "0.17.5", features = ["tokio"] }
log = "0.4.21"
multiaddr = "0.18.1"
tempfile = "3.10.1"
xor_name = "5.0.0"
color-eyre = "0.6.3"
dirs-next = "2.0.0"
structopt = "0.3.26"
clap = "4.5.4"
walkdir = "2.5.0"
chrono = { version = "0.4.37", features = ["serde"] }
rmp-serde = "1.1.2"
self_encryption = "0.29.1"
crdts = "7.3.2"
url = "2.5.0"
regex = "1.9.6"
ring = "0.17.8"
prettytable = "0.10.0"
const-hex = "1.13.1"
hex = "0.4.3"
rpassword = "7.3.1"
thiserror = "2.0.0"
actix-web = "4.9.0"
mime_guess = "2.0.5"
schnellru = "0.2.4"
mime = "0.3.17"
blsttc = "8.0.2"
open = "5.3.2"
port_check = "0.2.1"
reqwest = { version = "0.12.12", features = ["json", "rustls-tls"], default-features = false }
utoipa = { version = "5.3.1", features = ["actix_extras"] }
utoipa-actix-web = "0.1.2"
utoipa-swagger-ui = { version = "9.0.1", features = ["reqwest", "actix-web"] }

# patched
#utoipa = { path = "../../utoipa-patch/utoipa", features = ["actix_extras"] } # "5.3.1"
#utoipa-actix-web = { path = "../../utoipa-patch/utoipa-actix-web/", version = "0.1.2" }
#utoipa-swagger-ui = { path = "../../utoipa-patch/utoipa-swagger-ui", version = "9.0.1", features = ["reqwest", "actix-web"] }

#[dependencies.zip]
# Downgrade zip until utoipa fix released
#version = "=2.3.0"

