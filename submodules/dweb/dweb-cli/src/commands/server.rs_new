/*
 Copyright (c) 2025- <PERSON>

 This program is free software: you can redistribute it and/or modify
 it under the terms of the GNU Affero General Public License as published by
 the Free Software Foundation, either version 3 of the License, or
 (at your option) any later version.

 This program is distributed in the hope that it will be useful,
 but WITHOUT ANY WARRANTY; without even the implied warranty of
 MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 GNU Affero
  Public License for more details.

 You should have received a copy of the GNU Affero General Public License
 along with this program. If not, see <https://www.gnu.org/licenses/>.
*/

use color_eyre::{eyre::eyre, Result};

use dweb::client::{ApiControl, DwebClient, DwebClientConfig};
use dweb::web::name::register_builtin_names;

pub(crate) async fn connect_and_announce(
    local_network: bool,
    alpha_network: bool,
    host: Option<String>,
    port: Option<u16>,
    api_control: ApiControl,
    announce: bool,
) -> (Dweb<PERSON><PERSON>, bool) {
    let client = dweb::client::DwebClient::initialise_and_connect(&DwebClientConfig {
        local_network,
        alpha_network,
        host,
        port,
        client: None,
        wallet: None,
        api_control,
    })
    .await
    .expect("Failed to connect to Autonomi Network");

    if announce {
        if local_network {
            println!("-> local network: {}", client.network);
        } else if alpha_network {
            println!("-> alpha network {}", client.network);
        } else {
            println!("-> public network {}", client.network);
        };
    };

    (client, local_network)
}

use dweb_server::DwebService;

pub(crate) async fn start_in_foreground_latest(
    local: bool,
    alpha: bool,
    api_control: ApiControl,
    host: Option<String>,
    port: Option<u16>,
    logdir: Option<String>,
) -> Result<bool> {
    register_builtin_names(local);

    // Start the main server (for port based browsing), which will handle /dweb-open URLs  opened by 'dweb open'

    let port = port.unwrap_or(dweb::web::DEFAULT_HTTP_PORT);
    let client_config = DwebClientConfig {
        host,
        port: Some(port),
        ..Default::default()
    };

    let mut dweb_service = DwebService::new(client_config);
    // dweb_service.start(port);
    dweb_service.start_blocking(port).await;
    Ok(true)
}

pub(crate) async fn start_in_foreground(
    local: bool,
    alpha: bool,
    api_control: ApiControl,
    host: Option<String>,
    port: Option<u16>,
    logdir: Option<String>,
) -> Result<bool> {
    let (client, is_local_network) =
        connect_and_announce(local, alpha, host, port, api_control, true).await;

    // Start the main server (for port based browsing), which will handle /dweb-open URLs  opened by 'dweb open'
    let host = client.host.clone();
    let port = client.port;
    register_builtin_names(is_local_network);
    match dweb_server::services::old_serve_with_ports(&client, None, false, local).await {
        Ok(_) => return Ok(true),
        Err(e) => {
            println!("{e:?}");
            return Err(eyre!(e));
        }
    }
}

pub async fn start_in_background(
    local: bool,
    alpha: bool,
    api_control: ApiControl,
    host: Option<String>,
    port: Option<u16>,
    logdir: Option<String>,
) {
}
