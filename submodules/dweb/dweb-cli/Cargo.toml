[package]
name = "dweb-cli"
description = "Decentralised web and storage command line app for Autonomi"
edition = "2021"
version.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true

[[bin]]
name = "dweb"
path = "src/main.rs"

[features]
# updated for autonomi v0.3.4
default = ["skip-network-compatibility-check"]
ametrics = ["ant-logging/process-metrics"]
development = ["fixed-dweb-hosts"]

skip-network-compatibility-check = []   # Avoid need to upload avp type and include xor address in build
fixed-dweb-hosts = []

[dependencies]
#
# Generated using: dw-dependencies --branch stable-2025.7.1.5
autonomi = { version = "0.5.3" }
ant-build-info = { version = "0.1.29" }
ant-logging = { version = "0.2.51" }
evmlib = { version = "0.4.2" }
#
# Generated using: dw-dependencies --branch stable-2025.7.1.3
#autonomi = { version = "0.5.2" }
#ant-build-info = { version = "0.1.29" }
#ant-logging = { version = "0.2.51" }
#evmlib = { version = "0.4.2" }

dweb = { path = "../dweb-lib", version = "0.10.9", features = ["development"]}
dweb-server = { path = "../dweb-server", version = "0.10.9", features = ["development"]}

clap = "4.5.21"
color-eyre = "0.6.3"
indicatif = "0.17.9"
log = "0.4.22"
regex = "1.11.1"
xor_name = "5.0.0"
chrono = "0.4.39"
tracing = "0.1.41"
tokio = { version = "1.42.0", features = [ "rt-multi-thread" ] }
url = "2.5.4"
env_logger = "0.11.6"
futures-util = "0.3.31"
mime = "0.3.17"
open = "5.3.2"
hex = "0.4.3"
blsttc = "8.0.2"
serde = "1.0.219"
serde_json = "1.0.139"
actix-web = "4.9.0"
actix-cors = "0.7.1"
utoipa = { version = "5.3.1", features = ["actix_extras", "non_strict_integers"] }
utoipa-actix-web = "0.1.2"
utoipa-swagger-ui = { version = "9.0.1", features = ["reqwest", "actix-web"] }
qstring = "0.7.2"
actix-multipart = "0.7.2"

# patched
#utoipa = { path = "../../utoipa-patch/utoipa", features = ["actix_extras", "non_strict_integers"] } # "5.3.1"
#utoipa-actix-web = { path = "../../utoipa-patch/utoipa-actix-web/", version = "0.1.2" }
#utoipa-swagger-ui = { path = "../../utoipa-patch/utoipa-swagger-ui", version = "9.0.1", features = ["reqwest", "actix-web"] }

#[dependencies.zip]
# Downgrade zip until utoipa fix released
#version = "=2.3.0"

[lints]
workspace = true
