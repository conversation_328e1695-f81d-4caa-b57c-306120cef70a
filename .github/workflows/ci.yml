name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  CARGO_TERM_COLOR: always

jobs:
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    steps:
      - name: Add Codeberg to known hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan -H codeberg.org >> ~/.ssh/known_hosts

      - name: Setup SSH key for submodules
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.CODEBERG_SSH_KEY }}

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          components: rustfmt, clippy

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: src-tauri

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf libglib2.0-dev libgtk-3-dev

      - name: Install frontend dependencies
        run: npm ci

      #FIXME: reenable this check when all errors/warnings are resolved
      #- name: Check frontend
      #  run: npm run check

      - name: Build frontend
        run: npm run build

      - name: Check Rust formatting
        run: cargo fmt --all --check
        working-directory: src-tauri

      - name: Run Clippy
        run: cargo clippy --all-targets --all-features -- -D warnings
        working-directory: src-tauri

      - name: Run Rust tests
        run: cargo test
        working-directory: src-tauri

      - name: Test Tauri build (no bundle)
        run: cargo build
        working-directory: src-tauri
        env:
          # Ensure we don't try to create bundles in CI
          TAURI_SKIP_DEVSERVER_CHECK: true
