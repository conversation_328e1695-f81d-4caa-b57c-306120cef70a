* libraries and technologies to use
** veilid (rust)
  - https://veilid.com/ (main page)
  - https://gitlab.com/veilid/veilid/-/tree/main/veilid-core (core library)
  - P2P communication layer
    - temp data store
    - onion routing
    - DHT
    - application messaging
      - RPC
    - instant messaging and forum support backbone
*** app notifications
  - and then when someone in the group writes to the record, others could get a watch_dht_values() notification for when the record changes
    that would limit the blast radius, and get you the effect you're looking for.
    people would subscribe to your group by you handing them a dht secret key
    and then they could write notifications to the record created for your group
    and subscribe to changes to that record with the lastest announcements
    i think you can totally do what you're talking about today, just with a bit more guardrails on the design than 'every user of my app automatically
    is subscribed to an app-wide notifications firehose'
    anyway, the magic you're looking for is watch_dht_values, check the rustdoc and you can read on it.
** internet computer protocol (rust)
  - smart contract to automatically convert from one coin/token to Autonomi/ETH for buying storage
    - DeFi so should be OK
  - could also be used to write to a single data store
** zk-STARK solver (rust)
  - https://github.com/starkware-libs/stwo
  - prove that computation is correct without rerunning and/or exposing secret inputs
** garble (rust) and polytune (rust)
  - https://github.com/sine-fdn/polytune (MPC implementation)
  - https://garble-lang.org/landing.html (language)
  - https://sine.foundation/library/002-smpc (high level overview)
  - multi-party computation
  - Used to combine data from multiple users without revealing the secret data making up the computation
    - anonymous metrics tracking for pay-the-creator!
    - Could be used for smart contracts or writing to a common directory?
* Ideas
 - use the WASM OS as a pointer to open program files
   - Specify this opener (just like dweb or anttp)
   - download the opener from the address posted
     - cache this installation
     - program is a compiled WASM image that just runs in the backend and has rust start/stop functions
   - run the opener with the specified input files
 - if we could transfer ownership of scratchpads, we could transfer this file between users and it has an app container encrypted with my key
   - then when I transfer it to you, you now have the key to this app container
   - the purpose of the container could be to execute as a DBC signing bank for funds that exist in that container's keys
*** HUGE
   - what if tokens weren't just a number, they were a programmable entity on the network
     - the program itself is an encrypted container that you can feed inputs into and it will process them
   - to find them you have a 
** QUESTION: is there a way to store the opener application as an encrypted file?
  - several nodes can run this application and provide back the answer as a pool
    - most must agree or the operation is cancelled to the user
  - if most agree, other nodes are running zk-STARK solvers to make sure the answer is good compared to a pool of good runners
    - same with the original answer nodes, the zk-STARK solvers must also all agree
    - answers are generated by MPC using polytune and garble
  - the nodes run these OS containers in an encrypted container environment
    - the nodes don't know what they are computing and only see the encrypted inputs and outputs
  - the inputs and outputs are propagated through onion routing so there is no knowledge of where the request came from or where it is going
    - maybe each layer only solves part of the solution
    - machines just pass pointers to shards of encrypted application containers and execute them
  - now the question of payment: is this a shared free service or will it cost a token?
    - I think tokens are good. It enables users to collect income from their hardware
      - prevents people from taking over by making things too expensive
      - for example, if you put in such an amount of TC (tera-cycles), you should be able to get a ratio of that work back (depending on how many users are running stuff)
      - so if you buy a $3200 compute engine for the network, you should get $3200/the number machines creating and checking the work
        - maybe divided by $3200/8 = $400
      - a machine that cost $400 running 24/7/365 can do a lot of work
    - This also means, you can buy compute power that you don't have, for example, for running AI's!!!!
      - specify the hardware required (perhaps video card)
    - OS runners don't even have to be WASM, you could come up with rust compute functions to run natively
  - for speed up, when caching app containers, need to have a way that keeps them encrypted without the keys so they can't run
** implementation questions
  - Token should be a DBC?
  - Can we use Veilid as the communication layer with the onion routing?
  - 
